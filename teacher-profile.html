<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师简介 - 张明老师</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }

        /* 头部区域 */
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 20px 30px;
            text-align: center;
            position: relative;
        }

        .avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            border: 4px solid rgba(255,255,255,0.3);
            margin: 0 auto 20px;
            background: #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: #999;
        }

        .teacher-name {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .teacher-title {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 15px;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 4px;
        }

        /* 内容区域 */
        .content {
            padding: 0 20px;
        }

        .section {
            margin: 30px 0;
            padding: 25px 20px;
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            border-left: 4px solid #667eea;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .section-title::before {
            content: '';
            width: 6px;
            height: 6px;
            background: #667eea;
            border-radius: 50%;
            margin-right: 10px;
        }

        .intro-text {
            font-size: 15px;
            line-height: 1.8;
            color: #666;
            margin-bottom: 15px;
        }

        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
        }

        /* 教学风格 */
        .style-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }

        .tag {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        /* 学员评价 */
        .review {
            background: #f8f9ff;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            border-left: 3px solid #667eea;
        }

        .review-text {
            font-size: 14px;
            color: #555;
            font-style: italic;
            margin-bottom: 10px;
        }

        .reviewer {
            font-size: 12px;
            color: #999;
            text-align: right;
        }

        /* 联系按钮 */
        .contact-section {
            padding: 30px 20px;
            text-align: center;
        }

        .contact-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .contact-btn:hover {
            transform: translateY(-2px);
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .container {
                max-width: 100%;
            }
            
            .header {
                padding: 30px 15px 25px;
            }
            
            .content {
                padding: 0 15px;
            }
            
            .section {
                margin: 20px 0;
                padding: 20px 15px;
            }
        }

        /* 动画效果 */
        .section {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 头部区域 -->
        <div class="header">
            <div class="avatar">👨‍🏫</div>
            <h1 class="teacher-name">张明老师</h1>
            <p class="teacher-title">高级数学教师 · 教学主任</p>
            
            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">8</span>
                    <span class="stat-label">教学年限</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">500+</span>
                    <span class="stat-label">学员数量</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">95%</span>
                    <span class="stat-label">提分率</span>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 教学简介 -->
            <div class="section">
                <h2 class="section-title">教学简介</h2>
                <p class="intro-text">
                    毕业于<span class="highlight">北京师范大学数学系</span>，拥有8年一线教学经验。专注于高中数学教学，
                    擅长将复杂的数学概念用简单易懂的方式讲解，帮助学生建立完整的知识体系。
                </p>
                <p class="intro-text">
                    累计辅导学员500余名，其中<span class="highlight">85%的学员数学成绩提升30分以上</span>，
                    多名学员考入985、211重点院校。注重因材施教，针对不同基础的学生制定个性化学习方案。
                </p>
            </div>

            <!-- 教学风格 -->
            <div class="section">
                <h2 class="section-title">教学风格</h2>
                <div class="style-tags">
                    <span class="tag">逻辑清晰</span>
                    <span class="tag">耐心细致</span>
                    <span class="tag">方法独特</span>
                    <span class="tag">激发兴趣</span>
                </div>
                <p class="intro-text">
                    "数学不是死记硬背，而是思维的艺术。我希望每一位学生都能在数学的世界里找到属于自己的乐趣，
                    用理性的思维去探索未知，用严谨的态度去解决问题。"
                </p>
            </div>

            <!-- 学员评价 -->
            <div class="section">
                <h2 class="section-title">学员评价</h2>
                
                <div class="review">
                    <p class="review-text">
                        "张老师的课程让我对数学有了全新的认识，原本觉得很难的函数和导数，
                        在老师的讲解下变得清晰明了。高考数学提升了40分！"
                    </p>
                    <p class="reviewer">—— 李同学，2023届毕业生</p>
                </div>

                <div class="review">
                    <p class="review-text">
                        "老师非常有耐心，会根据我的学习情况调整教学方法。
                        每次课后都会给我布置针对性的练习，进步很明显。"
                    </p>
                    <p class="reviewer">—— 王同学，高三在读</p>
                </div>

                <div class="review">
                    <p class="review-text">
                        "张老师不仅教会了我解题技巧，更重要的是培养了我的数学思维。
                        现在遇到难题也不会慌张，知道如何分析和解决。"
                    </p>
                    <p class="reviewer">—— 陈同学，已考入清华大学</p>
                </div>
            </div>
        </div>

        <!-- 联系区域 -->
        <div class="contact-section">
            <button class="contact-btn" onclick="contactTeacher()">
                立即咨询课程
            </button>
        </div>
    </div>

    <script>
        function contactTeacher() {
            alert('请联系客服微信：teacher_zhang 或拨打电话：400-123-4567');
        }

        // 简单的滚动动画
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                const rect = section.getBoundingClientRect();
                if (rect.top < window.innerHeight && rect.bottom > 0) {
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }
            });
        });
    </script>
</body>
</html>
