<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教师简介 - 金膛教练</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: linear-gradient(180deg, #fff5e6 0%, #fff 20%, #fff 100%);
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* 几何装饰元素 */
        .container::before {
            content: '';
            position: absolute;
            top: -50px;
            right: -50px;
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 50%;
            opacity: 0.3;
            z-index: 1;
        }

        .container::after {
            content: '';
            position: absolute;
            bottom: 100px;
            left: -30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #ffecd2 0%, #fcb69f 100%);
            transform: rotate(45deg);
            opacity: 0.4;
            z-index: 1;
        }

        /* 更多几何装饰元素 */
        .geometric-decoration {
            position: absolute;
            z-index: 1;
        }

        .geometric-decoration:nth-child(1) {
            top: 200px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border-radius: 50%;
            opacity: 0.2;
        }

        .geometric-decoration:nth-child(2) {
            top: 400px;
            left: 10px;
            width: 30px;
            height: 30px;
            background: linear-gradient(45deg, #ffecd2 0%, #fcb69f 100%);
            transform: rotate(30deg);
            opacity: 0.3;
        }

        .geometric-decoration:nth-child(3) {
            bottom: 200px;
            right: 15px;
            width: 25px;
            height: 25px;
            background: linear-gradient(90deg, #ff9a9e 0%, #ffecd2 100%);
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
            opacity: 0.25;
        }

        /* 头部区域 - 占页面一半 */
        .header {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #ffecd2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
            height: 50vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            z-index: 2;
        }

        .avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            border: 4px solid rgba(255,255,255,0.8);
            margin: 0 auto 20px;
            background: #fff;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 50px;
            color: #ff9a9e;
            position: relative;
            box-shadow: 0 8px 25px rgba(255, 154, 158, 0.3);
        }

        .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
        }

        .teacher-name {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .teacher-title {
            font-size: 18px;
            opacity: 0.95;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 4px;
        }

        /* 内容区域 */
        .content {
            padding: 15px 20px 0;
            position: relative;
            z-index: 2;
        }

        .section {
            margin: 15px 0;
            padding: 20px 0;
            position: relative;
        }

        .section:not(:last-child)::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #ff9a9e 0%, #fecfef 50%, #ffecd2 100%);
            opacity: 0.3;
        }

        .section-title {
            font-size: 22px;
            font-weight: 700;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            position: relative;
            padding-left: 30px;
        }

        .section-title::before {
            content: '';
            position: absolute;
            left: 0;
            width: 20px;
            height: 20px;
            background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 100%);
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
        }

        .section-title::after {
            content: '';
            position: absolute;
            left: 8px;
            top: 50%;
            width: 4px;
            height: 4px;
            background: #fff;
            border-radius: 50%;
            transform: translateY(-50%);
        }

        .intro-text {
            font-size: 15px;
            line-height: 1.8;
            color: #666;
            margin-bottom: 15px;
        }

        .highlight {
            background: linear-gradient(120deg, #ffecd2 0%, #fcb69f 100%);
            padding: 3px 8px;
            border-radius: 6px;
            font-weight: 600;
            color: #8b4513;
        }

        /* 教学风格 */
        .style-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 15px 0;
        }

        .tag {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            color: white;
            padding: 10px 18px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(255, 154, 158, 0.3);
        }

        /* 学员评价 */
        .review {
            background: linear-gradient(135deg, #fff5f5 0%, #ffeee6 100%);
            padding: 18px;
            border-radius: 15px;
            margin: 12px 0;
            border-left: 4px solid #ff9a9e;
            position: relative;
        }

        .review::before {
            content: '"';
            position: absolute;
            top: -5px;
            left: 15px;
            font-size: 40px;
            color: #ff9a9e;
            opacity: 0.3;
        }

        .review-text {
            font-size: 14px;
            color: #555;
            font-style: italic;
            margin-bottom: 10px;
        }

        .reviewer {
            font-size: 12px;
            color: #999;
            text-align: right;
        }

        /* 联系按钮 */
        .contact-section {
            padding: 25px 20px 30px;
            text-align: center;
            position: relative;
        }

        .contact-btn {
            background: linear-gradient(45deg, #ff9a9e, #fecfef);
            color: white;
            padding: 18px 45px;
            border: none;
            border-radius: 30px;
            font-size: 18px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 6px 20px rgba(255, 154, 158, 0.4);
            position: relative;
            overflow: hidden;
        }

        .contact-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .contact-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(255, 154, 158, 0.5);
        }

        .contact-btn:hover::before {
            left: 100%;
        }

        /* 响应式设计 */
        @media (max-width: 480px) {
            .container {
                max-width: 100%;
            }
            
            .header {
                padding: 30px 15px 25px;
            }
            
            .content {
                padding: 0 15px;
            }
            
            .section {
                margin: 20px 0;
                padding: 20px 15px;
            }
        }

        /* 动画效果 */
        .section {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 几何装饰元素 -->
        <div class="geometric-decoration"></div>
        <div class="geometric-decoration"></div>
        <div class="geometric-decoration"></div>

        <!-- 头部区域 -->
        <div class="header">
            <div class="avatar">
                <img src="teacher-avatar.jpg" alt="金膛教练" onerror="this.style.display='none'; this.parentNode.style.fontSize='50px'; this.parentNode.innerHTML='👨‍🏫';">
            </div>
            <h1 class="teacher-name">金膛教练</h1>
            <p class="teacher-title">高级启迪伴学师</p>

            <div class="stats">
                <div class="stat-item">
                    <span class="stat-number">3</span>
                    <span class="stat-label">伴学教龄</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">1000+</span>
                    <span class="stat-label">服务学员</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">4</span>
                    <span class="stat-label">学科专长</span>
                </div>
            </div>
        </div>

        <!-- 内容区域 -->
        <div class="content">
            <!-- 教学简介 -->
            <div class="section">
                <h2 class="section-title">教练简介</h2>
                <p class="intro-text">
                    <strong>经验：</strong><span class="highlight">3年线下冲刺班伴学经验</span>，拥有丰富先进的带教理念，
                    累计服务学员1000+名，在学习引导和学员管理方面积累了丰富的实战经验。
                </p>
                <p class="intro-text">
                    <strong>专业能力：</strong>熟悉<span class="highlight">数学、物理、化学、生物</span>四大学科的学习方式引导，
                    能够针对不同学科特点制定个性化的学习计划，帮助学员建立完整的知识体系和学习方法。
                </p>
            </div>

            <!-- 教学风格 -->
            <div class="section">
                <h2 class="section-title">教学风格</h2>
                <div class="style-tags">
                    <span class="tag">认真负责</span>
                    <span class="tag">思路清晰</span>
                    <span class="tag">善于沟通</span>
                    <span class="tag">精准把控</span>
                </div>
                <p class="intro-text">
                    <strong>认真负责：</strong>善于与家长沟通，各方面深入了解学员学习情况，精通规划学习计划。
                </p>
                <p class="intro-text">
                    <strong>思路清晰：</strong>思维严谨，逻辑清晰，准确把控学科重难点易错点，善于归纳总结。
                </p>
                <p class="intro-text" style="font-style: italic; color: #667eea; font-weight: 500;">
                    💡 <strong>教练寄语：</strong>"别做知识的搬运工，成为它的建筑师！我递砖块，你设计自己的思维大厦。"
                </p>
            </div>

            <!-- 学员评价 -->
            <div class="section">
                <h2 class="section-title">学员评价</h2>

                <div class="review">
                    <p class="review-text">
                        "金教练真的很负责任！每次都会详细了解我的学习情况，
                        然后制定针对性的学习计划。在他的指导下，我的物理成绩提升了很多。"
                    </p>
                    <p class="reviewer">—— 张同学，高二在读</p>
                </div>

                <div class="review">
                    <p class="review-text">
                        "教练的思路特别清晰，能够准确找到我在数学学习中的薄弱环节，
                        给出的学习建议都很实用，让我少走了很多弯路。"
                    </p>
                    <p class="reviewer">—— 李同学，高三毕业生</p>
                </div>

                <div class="review">
                    <p class="review-text">
                        "金教练不仅帮我规划学习计划，还经常和我家长沟通学习进度。
                        他说的'成为知识的建筑师'这句话让我印象深刻，改变了我的学习方式。"
                    </p>
                    <p class="reviewer">—— 王同学，已考入重点大学</p>
                </div>
            </div>
        </div>

        <!-- 联系区域 -->
        <div class="contact-section">
            <button class="contact-btn" onclick="contactTeacher()">
                立即咨询课程
            </button>
        </div>
    </div>

    <script>
        function contactTeacher() {
            alert('请联系客服微信：coach_jintang 或拨打电话：400-123-4567');
        }

        // 简单的滚动动画
        window.addEventListener('scroll', function() {
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                const rect = section.getBoundingClientRect();
                if (rect.top < window.innerHeight && rect.bottom > 0) {
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }
            });
        });
    </script>
</body>
</html>
