<!DOCTYPE html>

<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>教师简介</title>
  <style>
    /* 全局样式 */
    :root {
      --primary-color: #2a7ed6;
      --secondary-color: #f5faff;
      --text-color: #333;
      --bg-color: #fff;
      --card-radius: 12px;
      --spacing: 16px;
    }
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: "PingFang SC", sans-serif; color: var(--text-color); background: var(--secondary-color); line-height: 1.6; }
    img { max-width: 100%; display: block; }
    a { text-decoration: none; color: inherit; }
    .container { width: 100%; max-width: 480px; margin: 0 auto; padding: var(--spacing); background: var(--bg-color); }
    button { border: none; border-radius: var(--card-radius); padding: 12px 24px; font-size: 1rem; background: var(--primary-color); color: #fff; cursor: pointer; }
    .section { margin-bottom: calc(var(--spacing) * 2); }

/* 首屏封面 */
.header { position: relative; text-align: center; padding-top: var(--spacing); padding-bottom: calc(var(--spacing) * 2); background: var(--primary-color);
  color: #fff; border-radius: 0 0 var(--card-radius) var(--card-radius);
}
.avatar { width: 120px; height: 120px; border-radius: 50%; border: 4px solid #fff; margin: 0 auto 8px; object-fit: cover; }
.header h1 { font-size: 1.5rem; margin-bottom: 4px; }
.header p { font-size: 1rem; opacity: 0.9; }

/* 卡片 */
.card { background: #fff; border-radius: var(--card-radius); padding: var(--spacing); box-shadow: 0 2px 8px rgba(0,0,0,0.1);}    

/* 教师简介 */
.intro-toggle { color: var(--primary-color); font-size: 0.9rem; margin-top: 8px; display: inline-block; cursor: pointer; }

/* 伴学精力 */
.stats { display: flex; justify-content: space-between; }
.stat-item { text-align: center; }
.stat-item .value { font-size: 1.25rem; font-weight: bold; }

/* 伴学风格 */
.styles { display: flex; flex-wrap: wrap; gap: var(--spacing); }
.style-item { flex: 1 1 calc(50% - var(--spacing)); display: flex; align-items: center; }
.style-item img { width: 24px; height: 24px; margin-right: 8px; }

/* 好评反馈 */
.feedback { position: relative; overflow: hidden; }
.feedback-list { display: flex; transition: transform 0.3s ease; }
.feedback-item { min-width: 100%; padding: var(--spacing); }

/* 底部CTA */
.footer { text-align: center; padding: var(--spacing) 0; }

  </style>
</head>
<body>
  <div class="container">
    <!-- 首屏封面 -->
    <header class="header section">
      <img src="74378c29-c92a-4dab-97c4-8c59278f8f6a.jpg" alt="教师照片" class="avatar" />
      <h1>张老师</h1>
      <p>数学 &bull; 8年教学经验</p>
      <p>精准提分，帮你轻松突破数学难关</p>
      <div style="margin-top: var(--spacing)">
        <button onclick="alert('预约试听')">预约试听</button>
      </div>
    </header>

<!-- 教师简介 -->
<section class="section card" id="intro">
  <h2>教师简介</h2>
  <p id="intro-text">张老师，硕士学历，国家级优秀教师。擅长中小学数学体系化教学，多次获得市级教学成果奖，拥有丰富的线上线下辅导经验。</p>
  <span class="intro-toggle" onclick="toggleIntro()">展开更多&gt;</span>
</section>

<!-- 伴学精力 -->
<section class="section card">
  <h2>伴学经历</h2>
  <div class="stats">
    <div class="stat-item">
      <div class="value">120h</div>
      <div>月均陪学</div>
    </div>
    <div class="stat-item">
      <div class="value">30次</div>
      <div>重点辅导</div>
    </div>
  </div>
</section>

<!-- 伴学风格 -->
<section class="section card">
  <h2>伴学风格</h2>
  <div class="styles">
    <div class="style-item"><img src="icon1.png" alt="严谨" /><span>严谨</span></div>
    <div class="style-item"><img src="icon2.png" alt="幽默" /><span>幽默</span></div>
    <div class="style-item"><img src="icon3.png" alt="鼓励" /><span>鼓励</span></div>
    <div class="style-item"><img src="icon4.png" alt="启发" /><span>启发</span></div>
  </div>
</section>

<!-- 好评反馈 -->
<section class="section card feedback">
  <h2>好评反馈</h2>
  <div class="feedback-list" id="feedbackList">
    <div class="feedback-item"><img src="feedback1.jpg" alt="反馈1" /></div>
    <div class="feedback-item"><img src="feedback2.jpg" alt="反馈2" /></div>
    <div class="feedback-item"><img src="feedback3.jpg" alt="反馈3" /></div>
  </div>
</section>

<!-- 底部CTA -->
<footer class="footer section">
  <button onclick="alert('立即咨询')">立即咨询</button>
</footer>

  </div>

  <script>
    // 教师简介展开/收起
    let introExpanded = false;
    function toggleIntro() {
      const p = document.getElementById('intro-text');
      const btn = p.nextElementSibling;
      if (!introExpanded) {
        p.textContent += ' 张老师具有多年一线教学经验，擅长个性化辅导，根据学生薄弱环节定制学习方案，引导学生主动思考。';
        btn.textContent = '收起';
      } else {
        p.textContent = '张老师，硕士学历，国家级优秀教师。擅长中小学数学体系化教学，多次获得市级教学成果奖，拥有丰富的线上线下辅导经验。';
        btn.textContent = '展开更多>'; 
      }
      introExpanded = !introExpanded;
    }
    // 简单轮播
    let current = 0;
    const list = document.getElementById('feedbackList');
    setInterval(() => {
      current = (current + 1) % list.children.length;
      list.style.transform = `translateX(-${current * 100}%)`;
    }, 3000);
  </script>

</body>
</html>
